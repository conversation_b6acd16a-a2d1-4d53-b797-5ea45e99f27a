/* 关键字体 - 立即加载 */
@font-face {
	font-family: "MatrixBoldSmallCaps";
	src: url("MatrixBoldSmallCaps.ttf") format("truetype"),
	     url("/fonts/MatrixBoldSmallCaps.ttf") format("truetype");
	font-weight: bold;
	font-display: swap;
}

@font-face {
	font-family: "en";
	src: url("en.ttf") format("truetype"),
	     url("/fonts/en.ttf") format("truetype");
	font-display: swap;
}

/* 非关键字体 - 懒加载，仅在需要时加载 */

/* 中文字体 */
@font-face {
	font-family: "zh";
	src: url("zh.ttf") format("truetype"),
	     url("/fonts/zh.ttf") format("truetype");
	font-display: swap;
	font-weight: normal;
	unicode-range: U+4E00-9FFF;
}

@font-face {
	font-family: "cn";
	src: url("cn.ttf") format("truetype"),
	     url("/fonts/cn.ttf") format("truetype");
	font-display: swap;
	font-weight: normal;
	unicode-range: U+4E00-9FFF;
}

/* 日语字体 */
@font-face {
	font-family: "jp";
	src: url("jp.ttf") format("truetype"),
	     url("/fonts/jp.ttf") format("truetype");
	font-display: swap;
	font-weight: normal;
	unicode-range: U+3040-309F, U+30A0-30FF, U+4E00-9FAF;
}

@font-face {
	font-family: "jp2";
	src: url("jp2.otf") format("opentype"),
	     url("/fonts/jp2.otf") format("opentype");
	font-display: swap;
	font-weight: normal;
	unicode-range: U+3040-309F, U+30A0-30FF, U+4E00-9FAF;
}

/* 日语字体别名 - 为了兼容性 */
@font-face {
	font-family: "ja2";
	src:url("jp2.otf") format("opentype");
	font-display: optional;
	unicode-range: U+3040-309F, U+30A0-30FF, U+4E00-9FAF;
}

/* 英语字体变体 */
@font-face {
	font-family: "en2";
	src:url("en2.ttf") format("truetype");
	font-display: optional;
}

@font-face {
	font-family: "en3";
	src:url("en3.ttf") format("truetype");
	font-display: optional;
}

/* 德语字体 - 使用拉丁字符集 */
@font-face {
	font-family: "de";
	src:url("en.ttf") format("truetype");
	font-display: optional;
	unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* 法语字体 - 使用拉丁字符集 */
@font-face {
	font-family: "fr";
	src:url("en.ttf") format("truetype");
	font-display: optional;
	unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* 西班牙语字体 - 使用拉丁字符集 */
@font-face {
	font-family: "es";
	src:url("en.ttf") format("truetype");
	font-display: optional;
	unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* 葡萄牙语字体 - 使用拉丁字符集 */
@font-face {
	font-family: "pt";
	src:url("en.ttf") format("truetype");
	font-display: optional;
	unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* 韩语字体 - 使用韩文字符集 */
@font-face {
	font-family: "ko";
	src:url("en.ttf") format("truetype");
	font-display: optional;
	unicode-range: U+AC00-D7AF, U+1100-11FF, U+3130-318F, U+A960-A97F, U+D7B0-D7FF;
}

/* 俄语字体 - 使用西里尔字符集 */
@font-face {
	font-family: "ru";
	src:url("en.ttf") format("truetype");
	font-display: optional;
	unicode-range: U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}

/* 希腊语字体 - 使用希腊字符集 */
@font-face {
	font-family: "el";
	src:url("en.ttf") format("truetype");
	font-display: optional;
	unicode-range: U+0370-03FF;
}

/* 泰语字体 - 使用泰文字符集 */
@font-face {
	font-family: "th";
	src:url("en.ttf") format("truetype");
	font-display: optional;
	unicode-range: U+0E00-0E7F;
}

/* 越南语字体 - 使用拉丁扩展字符集 */
@font-face {
	font-family: "vi";
	src:url("en.ttf") format("truetype");
	font-display: optional;
	unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB;
}

/* 特殊字体 */
@font-face {
	font-family: "link";
	src:url("link.ttf") format("truetype");
	font-display: optional;
}

/* 字体回退配置 - 为每种语言定义完整的字体栈 */
:root {
	/* 英语字体栈 */
	--font-en: "en", "en2", "en3", "Arial", "Helvetica", sans-serif;

	/* 中文字体栈 */
	--font-zh: "zh", "cn", "Noto Sans SC", "Noto Sans TC", "Microsoft YaHei", "微软雅黑", "SimSun", "宋体", sans-serif;

	/* 日语字体栈 */
	--font-ja: "jp", "jp2", "Noto Sans JP", "Hiragino Sans", "Yu Gothic", "Meiryo", sans-serif;

	/* 德语字体栈 */
	--font-de: "de", "en", "Arial", "Helvetica", "Roboto", sans-serif;

	/* 法语字体栈 */
	--font-fr: "fr", "en", "Arial", "Helvetica", "Roboto", sans-serif;

	/* 西班牙语字体栈 */
	--font-es: "es", "en", "Arial", "Helvetica", "Roboto", sans-serif;

	/* 葡萄牙语字体栈 */
	--font-pt: "pt", "en", "Arial", "Helvetica", "Roboto", sans-serif;

	/* 韩语字体栈 */
	--font-ko: "ko", "Noto Sans KR", "Malgun Gothic", "맑은 고딕", "Apple SD Gothic Neo", sans-serif;

	/* 俄语字体栈 */
	--font-ru: "ru", "en", "Arial", "Helvetica", "Roboto", sans-serif;

	/* 希腊语字体栈 */
	--font-el: "el", "en", "Arial", "Helvetica", "Roboto", sans-serif;

	/* 泰语字体栈 */
	--font-th: "th", "Noto Sans Thai", "Leelawadee UI", "Tahoma", sans-serif;

	/* 越南语字体栈 */
	--font-vi: "vi", "en", "Arial", "Helvetica", "Roboto", sans-serif;

	/* 特殊字体栈 */
	--font-matrix: "MatrixBoldSmallCaps", "Arial Black", "Helvetica", bold, sans-serif;
	--font-link: "link", "en", "Arial", sans-serif;
}
