# Cloudflare Pages 字体显示问题解决指南

## 问题概述

Yu-Gi-Oh卡牌制作器在部署到Cloudflare Pages后可能出现字体显示问题，主要表现为：
- 中文、日文等非拉丁字体无法正确显示
- 字体文件加载失败或显示为默认字体
- 字体加载缓慢或不一致

## 解决方案

### 1. 字体文件配置

确保所有字体文件都正确放置在 `static/fonts/` 目录中：

```
static/fonts/
├── MatrixBoldSmallCaps.ttf  # 关键字体
├── en.ttf                   # 英文字体
├── zh.ttf                   # 中文字体
├── cn.ttf                   # 中文字体备用
├── jp.ttf                   # 日文字体
├── jp2.otf                  # 日文字体备用
├── en2.ttf                  # 英文字体变体
├── en3.ttf                  # 英文字体变体
├── link.ttf                 # 特殊字体
└── font-face.css            # 字体定义文件
```

### 2. 字体CSS配置

`static/fonts/font-face.css` 文件已优化，包含双重路径配置：

```css
@font-face {
    font-family: "zh";
    src: url("zh.ttf") format("truetype"),
         url("/fonts/zh.ttf") format("truetype");
    font-display: swap;
    font-weight: normal;
    unicode-range: U+4E00-9FFF;
}
```

### 3. Nuxt配置优化

`nuxt.config.cloudflare.js` 已包含以下优化：

- **字体预加载配置**：关键字体文件预加载
- **nuxt-font-loader配置**：本地和Google Fonts配置
- **Webpack字体处理**：优化字体文件打包和路径

### 4. HTTP头配置

`_headers` 文件已配置正确的MIME类型：

```
/fonts/*.ttf
  Content-Type: font/ttf
  Cache-Control: public, max-age=31536000, immutable
  Access-Control-Allow-Origin: *

/fonts/*.otf
  Content-Type: font/otf
  Cache-Control: public, max-age=31536000, immutable
  Access-Control-Allow-Origin: *
```

## 构建和部署

### 1. 本地构建

```bash
# 使用Cloudflare专用配置构建
npm run build:cloudflare
```

这个命令会：
- 使用 `nuxt.config.cloudflare.js` 配置
- 生成静态文件
- 复制重定向规则
- 优化Cloudflare Pages部署
- 修复部署问题
- 验证字体配置

### 2. 字体验证

构建完成后，运行字体验证脚本：

```bash
npm run verify:fonts
```

这会检查：
- 字体文件是否存在
- 字体CSS配置是否正确
- HTML预加载配置是否正确
- HTTP头配置是否正确

### 3. 部署后调试

部署到Cloudflare Pages后，使用调试脚本检查字体加载：

```bash
# 设置你的域名并运行调试
CLOUDFLARE_DOMAIN=your-domain.pages.dev npm run debug:fonts
```

## 常见问题和解决方案

### 问题1: 字体文件404错误

**症状**: 浏览器控制台显示字体文件404错误

**解决方案**:
1. 确认字体文件在 `static/fonts/` 目录中
2. 检查构建后 `dist/fonts/` 目录是否包含字体文件
3. 验证 `_headers` 文件是否正确部署

### 问题2: 字体MIME类型错误

**症状**: 字体文件加载但不生效，控制台显示MIME类型警告

**解决方案**:
1. 确认 `_headers` 文件包含正确的Content-Type配置
2. 检查Cloudflare Pages是否正确应用了头配置

### 问题3: 中文/日文字体显示为默认字体

**症状**: 英文字体正常，但中文/日文显示为系统默认字体

**解决方案**:
1. 检查 `font-face.css` 中的unicode-range配置
2. 确认字体文件完整且未损坏
3. 验证字体栈配置是否正确

### 问题4: 字体加载缓慢

**症状**: 页面加载时字体闪烁或延迟显示

**解决方案**:
1. 确认关键字体已配置预加载
2. 检查font-display设置为swap
3. 优化字体文件大小

## 测试和验证

### 1. 字体测试页面

部署后访问 `/font-test.html` 页面测试所有字体：

```
https://your-domain.pages.dev/font-test.html
```

### 2. 浏览器开发者工具检查

1. 打开Network面板
2. 过滤Font类型请求
3. 检查字体文件的状态码和响应头
4. 确认Content-Type为正确的字体MIME类型

### 3. 字体加载状态检查

在浏览器控制台运行：

```javascript
// 检查字体加载状态
document.fonts.ready.then(() => {
    console.log('所有字体已加载完成');
    document.fonts.forEach(font => {
        console.log('已加载字体:', font.family, font.status);
    });
});

// 检查特定字体
const testFonts = ['en', 'zh', 'jp', 'MatrixBoldSmallCaps'];
testFonts.forEach(fontName => {
    if (document.fonts.check(`16px "${fontName}"`)) {
        console.log(`✅ ${fontName} 字体已加载`);
    } else {
        console.log(`❌ ${fontName} 字体未加载`);
    }
});
```

## 性能优化建议

1. **关键字体预加载**: 只预加载最重要的字体文件
2. **字体子集化**: 考虑为大型字体文件创建子集
3. **CDN缓存**: 利用Cloudflare的CDN缓存字体文件
4. **字体显示策略**: 使用适当的font-display值

## 支持的字体格式

- **TTF**: TrueType字体，广泛支持
- **OTF**: OpenType字体，支持更多特性
- **WOFF/WOFF2**: Web字体格式，压缩更好（可选升级）

## 联系和支持

如果遇到字体显示问题：

1. 首先运行 `npm run verify:fonts` 检查本地配置
2. 部署后运行 `npm run debug:fonts` 检查线上状态
3. 查看浏览器开发者工具的Network和Console面板
4. 检查字体测试页面的显示效果

通过以上步骤，应该能够解决大部分Cloudflare Pages上的字体显示问题。
