#!/usr/bin/env node

/**
 * Cloudflare Pages Font Verification Script
 * 验证字体文件在Cloudflare Pages部署后的加载情况
 */

const fs = require('fs')
const path = require('path')

console.log('🔍 开始验证Cloudflare Pages字体配置...')

const DIST_DIR = path.join(__dirname, '..', 'dist')
const STATIC_DIR = path.join(__dirname, '..', 'static')

/**
 * 检查字体文件是否存在
 */
function checkFontFiles() {
  console.log('📁 检查字体文件...')
  
  const fontFiles = [
    'MatrixBoldSmallCaps.ttf',
    'en.ttf',
    'zh.ttf',
    'cn.ttf',
    'jp.ttf',
    'jp2.otf',
    'en2.ttf',
    'en3.ttf',
    'link.ttf'
  ]

  const staticFontsDir = path.join(STATIC_DIR, 'fonts')
  const distFontsDir = path.join(DIST_DIR, 'fonts')

  console.log('📂 检查源字体文件 (static/fonts):')
  fontFiles.forEach(font => {
    const fontPath = path.join(staticFontsDir, font)
    if (fs.existsSync(fontPath)) {
      const stats = fs.statSync(fontPath)
      console.log(`  ✅ ${font} (${(stats.size / 1024).toFixed(2)} KB)`)
    } else {
      console.log(`  ❌ ${font} - 文件不存在`)
    }
  })

  console.log('\n📂 检查构建后字体文件 (dist/fonts):')
  if (fs.existsSync(distFontsDir)) {
    const distFonts = fs.readdirSync(distFontsDir)
    distFonts.forEach(font => {
      const fontPath = path.join(distFontsDir, font)
      const stats = fs.statSync(fontPath)
      console.log(`  ✅ ${font} (${(stats.size / 1024).toFixed(2)} KB)`)
    })
  } else {
    console.log('  ⚠️  dist/fonts 目录不存在')
  }
}

/**
 * 检查字体CSS文件
 */
function checkFontCSS() {
  console.log('\n🎨 检查字体CSS配置...')
  
  const fontCSSPath = path.join(STATIC_DIR, 'fonts', 'font-face.css')
  const distFontCSSPath = path.join(DIST_DIR, 'fonts', 'font-face.css')

  if (fs.existsSync(fontCSSPath)) {
    console.log('  ✅ 源字体CSS文件存在')
    
    const content = fs.readFileSync(fontCSSPath, 'utf8')
    
    // 检查关键字体定义
    const criticalFonts = ['MatrixBoldSmallCaps', 'en', 'zh', 'jp']
    criticalFonts.forEach(font => {
      if (content.includes(`font-family: "${font}"`)) {
        console.log(`    ✅ ${font} 字体定义存在`)
      } else {
        console.log(`    ❌ ${font} 字体定义缺失`)
      }
    })

    // 检查font-display设置
    if (content.includes('font-display: swap')) {
      console.log('    ✅ font-display: swap 配置正确')
    } else {
      console.log('    ⚠️  缺少 font-display: swap 配置')
    }

    // 检查unicode-range设置
    if (content.includes('unicode-range:')) {
      console.log('    ✅ unicode-range 配置存在')
    } else {
      console.log('    ⚠️  缺少 unicode-range 配置')
    }
  } else {
    console.log('  ❌ 源字体CSS文件不存在')
  }

  if (fs.existsSync(distFontCSSPath)) {
    console.log('  ✅ 构建后字体CSS文件存在')
  } else {
    console.log('  ❌ 构建后字体CSS文件不存在')
  }
}

/**
 * 检查HTML中的字体预加载
 */
function checkFontPreloads() {
  console.log('\n🔗 检查字体预加载配置...')
  
  const indexPath = path.join(DIST_DIR, 'index.html')
  
  if (fs.existsSync(indexPath)) {
    const content = fs.readFileSync(indexPath, 'utf8')
    
    const preloadFonts = [
      'MatrixBoldSmallCaps.ttf',
      'en.ttf',
      'zh.ttf',
      'jp.ttf'
    ]

    preloadFonts.forEach(font => {
      if (content.includes(`href="/fonts/${font}"`) && content.includes('rel="preload"')) {
        console.log(`  ✅ ${font} 预加载配置正确`)
      } else {
        console.log(`  ❌ ${font} 预加载配置缺失`)
      }
    })

    // 检查Google Fonts预连接
    if (content.includes('preconnect') && content.includes('fonts.googleapis.com')) {
      console.log('  ✅ Google Fonts 预连接配置正确')
    } else {
      console.log('  ⚠️  Google Fonts 预连接配置可能缺失')
    }
  } else {
    console.log('  ❌ index.html 文件不存在')
  }
}

/**
 * 检查_headers文件中的字体配置
 */
function checkFontHeaders() {
  console.log('\n📋 检查字体HTTP头配置...')
  
  const headersPath = path.join(__dirname, '..', '_headers')
  const distHeadersPath = path.join(DIST_DIR, '_headers')

  if (fs.existsSync(headersPath)) {
    const content = fs.readFileSync(headersPath, 'utf8')
    
    // 检查字体MIME类型
    const fontMimeTypes = ['font/ttf', 'font/otf', 'font/woff', 'font/woff2']
    fontMimeTypes.forEach(mimeType => {
      if (content.includes(mimeType)) {
        console.log(`  ✅ ${mimeType} MIME类型配置正确`)
      } else {
        console.log(`  ❌ ${mimeType} MIME类型配置缺失`)
      }
    })

    // 检查字体缓存配置
    if (content.includes('/fonts/*') && content.includes('max-age=31536000')) {
      console.log('  ✅ 字体缓存配置正确')
    } else {
      console.log('  ❌ 字体缓存配置缺失或不正确')
    }

    // 检查CORS配置
    if (content.includes('Access-Control-Allow-Origin: *')) {
      console.log('  ✅ 字体CORS配置正确')
    } else {
      console.log('  ⚠️  字体CORS配置可能缺失')
    }
  } else {
    console.log('  ❌ _headers 文件不存在')
  }

  if (fs.existsSync(distHeadersPath)) {
    console.log('  ✅ 构建后_headers文件存在')
  } else {
    console.log('  ❌ 构建后_headers文件不存在')
  }
}

/**
 * 生成字体加载测试页面
 */
function generateFontTestPage() {
  console.log('\n🧪 生成字体测试页面...')
  
  const testPageContent = `<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字体加载测试 - Yu-Gi-Oh Card Maker</title>
    <link rel="stylesheet" href="/fonts/font-face.css">
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .font-test { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .font-en { font-family: var(--font-en); }
        .font-zh { font-family: var(--font-zh); }
        .font-ja { font-family: var(--font-ja); }
        .font-matrix { font-family: var(--font-matrix); }
    </style>
</head>
<body>
    <h1>Yu-Gi-Oh Card Maker 字体加载测试</h1>
    
    <div class="font-test">
        <h3>英文字体测试</h3>
        <p class="font-en">This is a test of English fonts. ABCDEFGHIJKLMNOPQRSTUVWXYZ</p>
    </div>
    
    <div class="font-test">
        <h3>中文字体测试</h3>
        <p class="font-zh">这是中文字体测试。游戏王卡片制作器支持中文显示。</p>
    </div>
    
    <div class="font-test">
        <h3>日文字体测试</h3>
        <p class="font-ja">これは日本語フォントのテストです。遊戯王カードメーカー。</p>
    </div>
    
    <div class="font-test">
        <h3>Matrix字体测试</h3>
        <p class="font-matrix">MATRIX BOLD SMALL CAPS FONT TEST</p>
    </div>
    
    <script>
        // 检查字体加载状态
        document.fonts.ready.then(() => {
            console.log('所有字体已加载完成');
            document.fonts.forEach(font => {
                console.log('已加载字体:', font.family, font.status);
            });
        });
        
        // 检查特定字体是否加载
        const testFonts = ['en', 'zh', 'jp', 'MatrixBoldSmallCaps'];
        testFonts.forEach(fontName => {
            if (document.fonts.check(\`16px "\${fontName}"\`)) {
                console.log(\`✅ \${fontName} 字体已加载\`);
            } else {
                console.log(\`❌ \${fontName} 字体未加载\`);
            }
        });
    </script>
</body>
</html>`

  const testPagePath = path.join(DIST_DIR, 'font-test.html')
  
  try {
    fs.writeFileSync(testPagePath, testPageContent, 'utf8')
    console.log('  ✅ 字体测试页面已生成: /font-test.html')
    console.log('  📝 部署后可访问: https://your-domain.pages.dev/font-test.html')
  } catch (error) {
    console.log('  ❌ 生成字体测试页面失败:', error.message)
  }
}

/**
 * 主函数
 */
function main() {
  try {
    checkFontFiles()
    checkFontCSS()
    checkFontPreloads()
    checkFontHeaders()
    generateFontTestPage()
    
    console.log('\n🎉 字体验证完成!')
    console.log('\n📋 建议的后续步骤:')
    console.log('1. 确保所有字体文件都已正确复制到dist目录')
    console.log('2. 验证_headers文件包含正确的MIME类型配置')
    console.log('3. 部署后访问 /font-test.html 页面测试字体加载')
    console.log('4. 使用浏览器开发者工具检查字体文件的网络请求状态')
    console.log('5. 验证字体文件的Content-Type响应头是否正确')
    
  } catch (error) {
    console.error('❌ 字体验证过程中出现错误:', error.message)
    process.exit(1)
  }
}

if (require.main === module) {
  main()
}

module.exports = { checkFontFiles, checkFontCSS, checkFontPreloads, checkFontHeaders }
