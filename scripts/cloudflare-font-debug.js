#!/usr/bin/env node

/**
 * Cloudflare Pages 字体调试脚本
 * 用于在部署后诊断字体加载问题
 */

const https = require('https')
const http = require('http')

// 配置你的Cloudflare Pages域名
const DOMAIN = process.env.CLOUDFLARE_DOMAIN || 'your-domain.pages.dev'
const PROTOCOL = 'https'

console.log(`🔍 开始检查 ${PROTOCOL}://${DOMAIN} 的字体加载情况...`)

/**
 * 发送HTTP请求
 */
function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const client = url.startsWith('https') ? https : http
    
    client.get(url, (res) => {
      let data = ''
      
      res.on('data', (chunk) => {
        data += chunk
      })
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: data
        })
      })
    }).on('error', (err) => {
      reject(err)
    })
  })
}

/**
 * 检查字体文件的HTTP响应
 */
async function checkFontFile(fontPath) {
  const url = `${PROTOCOL}://${DOMAIN}${fontPath}`
  
  try {
    console.log(`📁 检查字体文件: ${fontPath}`)
    const response = await makeRequest(url)
    
    console.log(`  状态码: ${response.statusCode}`)
    console.log(`  Content-Type: ${response.headers['content-type'] || '未设置'}`)
    console.log(`  Content-Length: ${response.headers['content-length'] || '未知'} bytes`)
    console.log(`  Cache-Control: ${response.headers['cache-control'] || '未设置'}`)
    console.log(`  Access-Control-Allow-Origin: ${response.headers['access-control-allow-origin'] || '未设置'}`)
    
    if (response.statusCode === 200) {
      console.log(`  ✅ ${fontPath} 加载成功`)
    } else {
      console.log(`  ❌ ${fontPath} 加载失败 (${response.statusCode})`)
    }
    
    return response.statusCode === 200
  } catch (error) {
    console.log(`  ❌ ${fontPath} 请求失败: ${error.message}`)
    return false
  }
}

/**
 * 检查字体CSS文件
 */
async function checkFontCSS() {
  const cssPath = '/fonts/font-face.css'
  const url = `${PROTOCOL}://${DOMAIN}${cssPath}`
  
  try {
    console.log(`🎨 检查字体CSS文件: ${cssPath}`)
    const response = await makeRequest(url)
    
    console.log(`  状态码: ${response.statusCode}`)
    console.log(`  Content-Type: ${response.headers['content-type'] || '未设置'}`)
    
    if (response.statusCode === 200) {
      console.log(`  ✅ 字体CSS文件加载成功`)
      
      // 检查CSS内容
      const cssContent = response.data
      const fontFamilies = ['MatrixBoldSmallCaps', 'en', 'zh', 'jp', 'cn']
      
      fontFamilies.forEach(family => {
        if (cssContent.includes(`font-family: "${family}"`)) {
          console.log(`    ✅ ${family} 字体定义存在`)
        } else {
          console.log(`    ❌ ${family} 字体定义缺失`)
        }
      })
      
      return true
    } else {
      console.log(`  ❌ 字体CSS文件加载失败 (${response.statusCode})`)
      return false
    }
  } catch (error) {
    console.log(`  ❌ 字体CSS文件请求失败: ${error.message}`)
    return false
  }
}

/**
 * 检查主页面的字体预加载
 */
async function checkFontPreloads() {
  const url = `${PROTOCOL}://${DOMAIN}/`
  
  try {
    console.log(`🔗 检查主页面的字体预加载配置`)
    const response = await makeRequest(url)
    
    if (response.statusCode === 200) {
      const htmlContent = response.data
      
      const preloadFonts = [
        'MatrixBoldSmallCaps.ttf',
        'en.ttf',
        'zh.ttf',
        'jp.ttf'
      ]
      
      preloadFonts.forEach(font => {
        if (htmlContent.includes(`href="/fonts/${font}"`) && htmlContent.includes('rel="preload"')) {
          console.log(`  ✅ ${font} 预加载配置正确`)
        } else {
          console.log(`  ❌ ${font} 预加载配置缺失`)
        }
      })
      
      // 检查Google Fonts预连接
      if (htmlContent.includes('preconnect') && htmlContent.includes('fonts.googleapis.com')) {
        console.log(`  ✅ Google Fonts 预连接配置正确`)
      } else {
        console.log(`  ⚠️  Google Fonts 预连接配置可能缺失`)
      }
      
      return true
    } else {
      console.log(`  ❌ 主页面加载失败 (${response.statusCode})`)
      return false
    }
  } catch (error) {
    console.log(`  ❌ 主页面请求失败: ${error.message}`)
    return false
  }
}

/**
 * 生成字体加载测试报告
 */
function generateTestReport(results) {
  console.log('\n📊 字体加载测试报告')
  console.log('=' * 50)
  
  const totalFonts = results.fontFiles.length
  const successfulFonts = results.fontFiles.filter(result => result.success).length
  
  console.log(`字体文件测试: ${successfulFonts}/${totalFonts} 成功`)
  console.log(`字体CSS文件: ${results.fontCSS ? '✅ 成功' : '❌ 失败'}`)
  console.log(`字体预加载配置: ${results.preloads ? '✅ 成功' : '❌ 失败'}`)
  
  if (successfulFonts === totalFonts && results.fontCSS && results.preloads) {
    console.log('\n🎉 所有字体配置检查通过！')
  } else {
    console.log('\n⚠️  发现字体配置问题，请检查以下项目:')
    
    if (successfulFonts < totalFonts) {
      console.log('- 某些字体文件无法访问，检查文件是否正确部署')
    }
    
    if (!results.fontCSS) {
      console.log('- 字体CSS文件无法访问，检查文件路径和部署配置')
    }
    
    if (!results.preloads) {
      console.log('- 字体预加载配置有问题，检查HTML头部配置')
    }
    
    console.log('\n🔧 建议的修复步骤:')
    console.log('1. 确认字体文件已正确复制到 dist/fonts/ 目录')
    console.log('2. 检查 _headers 文件中的MIME类型配置')
    console.log('3. 验证 nuxt.config.cloudflare.js 中的字体预加载配置')
    console.log('4. 确认 static/fonts/font-face.css 中的字体路径正确')
  }
}

/**
 * 主函数
 */
async function main() {
  if (DOMAIN === 'your-domain.pages.dev') {
    console.log('⚠️  请设置环境变量 CLOUDFLARE_DOMAIN 为你的实际域名')
    console.log('例如: CLOUDFLARE_DOMAIN=yugiohcardmaker.pages.dev npm run debug:fonts')
    process.exit(1)
  }
  
  const results = {
    fontFiles: [],
    fontCSS: false,
    preloads: false
  }
  
  // 检查关键字体文件
  const fontFiles = [
    '/fonts/MatrixBoldSmallCaps.ttf',
    '/fonts/en.ttf',
    '/fonts/zh.ttf',
    '/fonts/cn.ttf',
    '/fonts/jp.ttf',
    '/fonts/jp2.otf',
    '/fonts/en2.ttf',
    '/fonts/en3.ttf',
    '/fonts/link.ttf'
  ]
  
  console.log('🔍 开始检查字体文件...\n')
  
  for (const fontPath of fontFiles) {
    const success = await checkFontFile(fontPath)
    results.fontFiles.push({ path: fontPath, success })
    console.log('') // 空行分隔
  }
  
  // 检查字体CSS
  results.fontCSS = await checkFontCSS()
  console.log('') // 空行分隔
  
  // 检查字体预加载
  results.preloads = await checkFontPreloads()
  console.log('') // 空行分隔
  
  // 生成报告
  generateTestReport(results)
}

if (require.main === module) {
  main().catch(error => {
    console.error('❌ 字体调试过程中出现错误:', error.message)
    process.exit(1)
  })
}

module.exports = { checkFontFile, checkFontCSS, checkFontPreloads }
